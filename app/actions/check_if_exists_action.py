import os
import traceback
import time
import math
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class CheckIfExistsAction(BaseAction):
    def execute(self, params):
        """
        Execute the Check if Exists action.
        This action checks if an element exists on the screen using various locator types.

        Args:
            params (dict): Parameters for the action
                - locator_type (str): Type of locator (id, xpath, accessibility_id, class, name, text, image)
                - locator_value (str): Value of the locator
                - threshold (float, optional): Similarity threshold for image matching (0.0-1.0). Default is 0.7
                - timeout (int, optional): Maximum time to wait for the element in seconds. Default is 10
                - use_env_var (bool, optional): Whether locator_value is an environment variable name
                - env_var_name (str, optional): Environment variable name if use_env_var is True

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
                - found (bool): Whether the element was found
        """
        try:
            # Check if controller is available
            if not self.controller:
                return {"status": "error", "message": "Device controller not available"}

            # Get parameters
            locator_type = params.get('locator_type')
            locator_value = params.get('locator_value')
            threshold = float(params.get('threshold', 0.7))
            timeout = int(params.get('timeout', 10))
            use_env_var = params.get('use_env_var', False)
            env_var_name = params.get('env_var_name')

            if not locator_type:
                return {"status": "error", "message": "Locator type is required"}

            # Handle environment variables
            if use_env_var and env_var_name:
                # Resolve environment variable
                env_value = os.environ.get(env_var_name)
                if env_value is None:
                    return {"status": "error", "message": f"Environment variable '{env_var_name}' not found"}
                locator_value = env_value
                self.logger.info(f"Resolved environment variable '{env_var_name}' to '{locator_value}'")

            if not locator_value:
                return {"status": "error", "message": "Locator value is required"}

            self.logger.info(f"Checking if element exists with {locator_type}: {locator_value}, timeout={timeout}s")

            # Handle image-based existence check
            if locator_type == 'image':
                return self._check_image_exists(locator_value, threshold, timeout)
            
            # Handle text-based existence check
            elif locator_type == 'text':
                return self._check_text_exists(locator_value, timeout)
            
            # Handle other locator types (id, xpath, accessibility_id, etc.)
            else:
                return self._check_element_exists(locator_type, locator_value, timeout)

        except Exception as e:
            self.logger.error(f"Error executing Check if Exists action: {e}")
            traceback.print_exc()
            return {"status": "error", "message": f"Check if Exists action failed: {str(e)}"}

    def _check_image_exists(self, image_filename, threshold, timeout):
        """Check if image exists on screen using the same logic as Tap (Image) action"""
        try:
            self.logger.info(f"Checking if image exists: '{image_filename}' with threshold={threshold}, timeout={timeout}s")

            # Get the absolute path to the reference image
            reference_images_dir = os.path.join(os.getcwd(), 'reference_images')
            abs_path = os.path.join(reference_images_dir, image_filename)

            if not os.path.exists(abs_path):
                return {
                    "status": "success", 
                    "message": f"Reference image not found: {image_filename}",
                    "found": False
                }

            # Use the controller's find_image method directly (same as Tap Image action)
            if not hasattr(self.controller, 'find_image'):
                return {"status": "error", "message": "Device controller does not support image recognition"}

            # Try to find the image on screen using the controller's method
            match_pos = self.controller.find_image(abs_path, threshold=threshold, timeout=timeout)

            if match_pos:
                # Validate coordinates to prevent infinity or NaN values
                device_width = None
                device_height = None

                # Try to get device dimensions if available
                if hasattr(self.controller, 'get_device_size'):
                    try:
                        device_size = self.controller.get_device_size()
                        if device_size and len(device_size) == 2:
                            device_width, device_height = device_size
                    except Exception as size_err:
                        self.logger.warning(f"Failed to get device size: {size_err}")

                # Validate the coordinates
                valid_coords = validate_coordinates(match_pos, device_width, device_height)

                if valid_coords:
                    x, y = valid_coords
                    self.logger.info(f"Image found at valid position: ({x}, {y})")
                    return {
                        "status": "success", 
                        "message": f"Image {image_filename} found at position ({x}, {y})",
                        "found": True,
                        "position": (x, y)
                    }
                else:
                    # Image found but coordinates are invalid
                    self.logger.error(f"Image found but coordinates are invalid: {match_pos}")
                    return {
                        "status": "success", 
                        "message": f"Image {image_filename} found but coordinates are invalid",
                        "found": False
                    }
            else:
                # Image doesn't exist
                self.logger.info(f"Image not found: {image_filename}")
                return {
                    "status": "success", 
                    "message": f"Image {image_filename} not found on screen",
                    "found": False
                }

        except Exception as e:
            self.logger.error(f"Error checking if image exists: {e}")
            return {"status": "error", "message": f"Error checking if image exists: {str(e)}"}

    def _check_text_exists(self, text_to_find, timeout):
        """Check if text exists on screen using OCR-based text detection"""
        try:
            self.logger.info(f"Checking if text exists on screen: '{text_to_find}' with timeout={timeout}s")

            # Method 1: Use OCR-based text detection (same as TapOnTextAction)
            try:
                # Take a screenshot first
                screenshot_result = self.controller.take_screenshot()
                if screenshot_result['status'] != 'success' or not screenshot_result['path']:
                    self.logger.warning("Failed to take screenshot for OCR text detection, falling back to XPath")
                else:
                    screenshot_path = screenshot_result['path']

                    # Import the text detection function
                    try:
                        from app.utils.text_detection import detect_text_in_image

                        # Create a directory for debug images
                        debug_dir = os.path.join(os.path.dirname(screenshot_path), 'text_detection')
                        os.makedirs(debug_dir, exist_ok=True)

                        # Try to find the text using OCR with timeout
                        start_time = time.time()
                        text_found = False

                        while time.time() - start_time < timeout and not text_found:
                            try:
                                # Find the text in the screenshot using OCR
                                result = detect_text_in_image(
                                    screenshot_path,
                                    text_to_find,
                                    output_dir=debug_dir
                                )

                                if result:
                                    text_found = True
                                    self.logger.info(f"Text '{text_to_find}' found on screen using OCR at coordinates: {result['coordinates']}")
                                    return {
                                        "status": "success",
                                        "message": f"Text '{text_to_find}' found on screen using OCR",
                                        "found": True,
                                        "coordinates": result['coordinates']
                                    }

                                # If not found and we have time left, take a new screenshot and try again
                                if time.time() - start_time < timeout - 1:  # Leave 1 second buffer
                                    time.sleep(1)  # Wait 1 second before retrying
                                    screenshot_result = self.controller.take_screenshot()
                                    if screenshot_result['status'] == 'success' and screenshot_result['path']:
                                        screenshot_path = screenshot_result['path']
                                else:
                                    break

                            except Exception as ocr_error:
                                self.logger.warning(f"OCR text detection failed: {ocr_error}")
                                break

                        if not text_found:
                            self.logger.info(f"Text '{text_to_find}' not found using OCR, trying XPath fallback")

                    except ImportError:
                        self.logger.warning("Text detection module not available, falling back to XPath")

            except Exception as screenshot_error:
                self.logger.warning(f"Screenshot-based text detection failed: {screenshot_error}, falling back to XPath")

            # Method 2: Fallback to XPath-based search
            if hasattr(self.controller, 'driver') and self.controller.driver:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.common.exceptions import TimeoutException
                from appium.webdriver.common.appiumby import AppiumBy

                driver = self.controller.driver

                # Create comprehensive XPath to search for text in various attributes
                xpath_patterns = [
                    f"//*[@text='{text_to_find}']",  # Exact text match
                    f"//*[contains(@text, '{text_to_find}')]",  # Contains text
                    f"//*[@content-desc='{text_to_find}']",  # Exact content description
                    f"//*[contains(@content-desc, '{text_to_find}')]",  # Contains content description
                    f"//*[@label='{text_to_find}']",  # Exact label (iOS)
                    f"//*[contains(@label, '{text_to_find}')]",  # Contains label (iOS)
                    f"//*[@name='{text_to_find}']",  # Exact name
                    f"//*[contains(@name, '{text_to_find}')]",  # Contains name
                    f"//*[@value='{text_to_find}']",  # Exact value
                    f"//*[contains(@value, '{text_to_find}')]"  # Contains value
                ]

                # Try each XPath pattern
                for i, xpath in enumerate(xpath_patterns):
                    try:
                        self.logger.debug(f"Trying XPath pattern {i+1}: {xpath}")
                        element = WebDriverWait(driver, 2).until(  # Short timeout per pattern
                            EC.presence_of_element_located((AppiumBy.XPATH, xpath))
                        )
                        self.logger.info(f"Text '{text_to_find}' found on screen using XPath pattern {i+1}")
                        return {
                            "status": "success",
                            "message": f"Text '{text_to_find}' found on screen using XPath",
                            "found": True
                        }
                    except TimeoutException:
                        continue  # Try next pattern
                    except Exception as xpath_error:
                        self.logger.debug(f"XPath pattern {i+1} failed: {xpath_error}")
                        continue

                # If no XPath patterns worked
                self.logger.info(f"Text '{text_to_find}' not found on screen using any method")
                return {
                    "status": "success",
                    "message": f"Text '{text_to_find}' not found on screen",
                    "found": False
                }
            else:
                return {
                    "status": "error",
                    "message": "No driver available for text search"
                }

        except Exception as e:
            self.logger.error(f"Error checking if text exists: {e}")
            return {"status": "error", "message": f"Error checking if text exists: {str(e)}"}

    def _check_element_exists(self, locator_type, locator_value, timeout):
        """Check if element exists on screen using locator type and value"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException, NoSuchElementException

            # Make sure we have a driver in device controller
            if not hasattr(self.controller, 'driver') or not self.controller.driver:
                return {"status": "error", "message": "No Appium driver available for element checking"}

            driver = self.controller.driver

            # Set up locator strategy
            locator = None
            if locator_type == 'id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ID, locator_value)
                self.logger.info(f"Checking for element with ID: {locator_value}")
            elif locator_type == 'xpath':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.XPATH, locator_value)
                self.logger.info(f"Checking for element with XPath: {locator_value}")
            elif locator_type == 'accessibility_id':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.ACCESSIBILITY_ID, locator_value)
                self.logger.info(f"Checking for element with Accessibility ID: {locator_value}")
            elif locator_type == 'class':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.CLASS_NAME, locator_value)
                self.logger.info(f"Checking for element with Class: {locator_value}")
            elif locator_type == 'name':
                from appium.webdriver.common.appiumby import AppiumBy
                locator = (AppiumBy.NAME, locator_value)
                self.logger.info(f"Checking for element with Name: {locator_value}")
            else:
                return {"status": "error", "message": f"Unsupported locator type: {locator_type}"}

            try:
                # Try to find the element with the specified timeout
                element = WebDriverWait(driver, timeout).until(
                    EC.presence_of_element_located(locator)
                )

                # Element found
                element_text = None
                try:
                    element_text = element.text
                except:
                    pass

                self.logger.info(f"Element found: {locator_type}='{locator_value}'" +
                                 (f" with text '{element_text}'" if element_text else ""))

                return {
                    "status": "success",
                    "message": f"Element found: {locator_type}='{locator_value}'",
                    "found": True,
                    "element_text": element_text
                }
            except TimeoutException:
                # Element not found within timeout
                self.logger.info(f"Element not found: {locator_type}='{locator_value}' within {timeout} seconds")
                return {
                    "status": "success",
                    "message": f"Element not found: {locator_type}='{locator_value}'",
                    "found": False
                }
            except Exception as find_err:
                self.logger.error(f"Error finding element: {str(find_err)}")
                return {
                    "status": "error",
                    "message": f"Error checking for element: {str(find_err)}"
                }
        except Exception as e:
            self.logger.error(f"Failed to execute exists check: {str(e)}")
            return {"status": "error", "message": f"Failed to execute exists check: {str(e)}"}
